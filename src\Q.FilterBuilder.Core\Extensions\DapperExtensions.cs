using System.Collections.Generic;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Dapper integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class DapperExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Dapper.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DapperQueryResult with query and parameter dictionary.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForDapper(filterGroup);
    /// var users = connection.Query&lt;User&gt;($"SELECT * FROM Users WHERE {result.WhereClause}", result.Parameters);
    /// </code>
    /// </example>
    public static DapperQueryResult BuildForDapper(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var (dapperQuery, dapperParameters) = ConvertToDapperFormat(query, parameters, filterBuilder.QueryFormatProvider);
        return new DapperQueryResult(dapperQuery, dapperParameters);
    }

    /// <summary>
    /// Converts a query and parameters to Dapper-compatible format.
    /// Handles provider-specific parameter naming and query conversion for Dapper compatibility.
    /// </summary>
    /// <param name="query">The original query</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="provider">The query format provider</param>
    /// <returns>A tuple with Dapper-compatible query and parameter dictionary</returns>
    private static (string query, Dictionary<string, object?> parameters) ConvertToDapperFormat(
        string query, object[] parameterValues, IQueryFormatProvider provider)
    {
        var paramDict = new Dictionary<string, object?>();
        var dapperQuery = query;

        for (var i = 0; i < parameterValues.Length; i++)
        {
            var originalParam = provider.FormatParameterName(i);
            string dapperParamName;

            // Handle provider-specific parameter conversion
            switch (provider.ParameterPrefix)
            {
                case "$": // PostgreSQL
                    // Convert $1, $2, etc. to @p0, @p1, etc. for Dapper compatibility
                    dapperParamName = $"@p{i}";
                    dapperQuery = dapperQuery.Replace(originalParam, dapperParamName);
                    break;

                case "?": // MySQL
                    // MySQL uses positional parameters, create unique names for Dapper
                    dapperParamName = $"p{i}";
                    break;

                default: // SQL Server and others
                    // Use the formatted parameter names as-is
                    dapperParamName = originalParam;
                    break;
            }

            paramDict[dapperParamName] = parameterValues[i];
        }

        return (dapperQuery, paramDict);
    }


}
