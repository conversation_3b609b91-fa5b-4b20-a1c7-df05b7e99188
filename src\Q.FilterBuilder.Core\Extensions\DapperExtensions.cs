using System.Collections.Generic;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Dapper integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class DapperExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Dapper.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DapperQueryResult with query and parameter dictionary.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForDapper(filterGroup);
    /// var users = connection.Query&lt;User&gt;($"SELECT * FROM Users WHERE {result.WhereClause}", result.Parameters);
    /// </code>
    /// </example>
    public static DapperQueryResult BuildForDapper(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var dapperParameters = CreateDapperParameters(parameters, filterBuilder.QueryFormatProvider);
        return new DapperQueryResult(query, dapperParameters);
    }

    /// <summary>
    /// Creates a parameter dictionary suitable for Dapper from parameter values.
    /// </summary>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="provider">The query format provider</param>
    /// <returns>A dictionary suitable for Dapper</returns>
    private static Dictionary<string, object?> CreateDapperParameters(object[] parameterValues, IQueryFormatProvider provider)
    {
        var paramDict = new Dictionary<string, object?>();
        for (var i = 0; i < parameterValues.Length; i++)
        {
            paramDict[provider.FormatParameterName(i)] = parameterValues[i];
        }
        return paramDict;
    }
}
