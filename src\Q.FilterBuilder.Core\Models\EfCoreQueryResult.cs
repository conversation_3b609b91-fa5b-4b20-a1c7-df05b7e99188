namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for Entity Framework Core.
/// Contains the WHERE clause, formatted query with placeholders, and parameter values for EF Core's FromSqlRaw method.
/// </summary>
public class EfCoreQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the query formatted for EF Core FromSqlRaw with {0}, {1} placeholders.
    /// Example: "[Name] = {0} AND [Age] > {1}"
    /// </summary>
    public string FormattedQuery { get; }

    /// <summary>
    /// Gets the parameter values in the correct order for the formatted query.
    /// These values correspond to the {0}, {1}, etc. placeholders in FormattedQuery.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Creates a new EF Core query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="formattedQuery">The EF Core formatted query with placeholders</param>
    /// <param name="parameters">The parameter values</param>
    public EfCoreQueryResult(string whereClause, string formattedQuery, object[] parameters)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        FormattedQuery = formattedQuery ?? throw new System.ArgumentNullException(nameof(formattedQuery));
        Parameters = parameters ?? throw new System.ArgumentNullException(nameof(parameters));
    }
}
