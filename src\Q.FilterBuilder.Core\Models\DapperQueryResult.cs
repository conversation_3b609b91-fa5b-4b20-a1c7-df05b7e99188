using System.Collections.Generic;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for Dapper.
/// Contains the WHERE clause and parameters formatted as a dictionary for <PERSON><PERSON>'s dynamic parameter binding.
/// </summary>
public class DapperQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the parameters formatted as a dictionary for Dapper.
    /// Keys are parameter names (e.g., "@p0", "@p1") and values are the parameter values.
    /// </summary>
    public Dictionary<string, object?> Parameters { get; }

    /// <summary>
    /// Creates a new Dapper query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameters">The Dapper parameter dictionary</param>
    public DapperQueryResult(string whereClause, Dictionary<string, object?> parameters)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        Parameters = parameters ?? throw new System.ArgumentNullException(nameof(parameters));
    }
}
