﻿using Microsoft.Extensions.DependencyInjection;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.PostgreSql.Extensions;
using Npgsql;
using Dapper;

// Create a simple test to debug the Dapper parameter issue
var services = new ServiceCollection();
services.AddPostgreSqlFilterBuilder();
var serviceProvider = services.BuildServiceProvider();
var filterBuilder = serviceProvider.GetRequiredService<IFilterBuilder>();

// Create a simple filter
var group = new FilterGroup("OR");
group.Rules.Add(new FilterRule("Name", "equal", "John Doe"));

// Test the core Build method
var (query, parameters) = filterBuilder.Build(group);
Console.WriteLine("Core Build Result:");
Console.WriteLine($"Query: {query}");
Console.WriteLine($"Parameters: [{string.Join(", ", parameters)}]");
Console.WriteLine();

// Test the Dapper extension
var dapperResult = filterBuilder.BuildForDapper(group);
Console.WriteLine("Dapper Extension Result:");
Console.WriteLine($"WhereClause: {dapperResult.WhereClause}");
Console.WriteLine($"Parameters: {string.Join(", ", dapperResult.Parameters.Select(p => $"{p.Key}={p.Value}"))}");
Console.WriteLine();

// Test what the provider returns
Console.WriteLine("Provider Details:");
Console.WriteLine($"ParameterPrefix: '{filterBuilder.QueryFormatProvider.ParameterPrefix}'");
for (int i = 0; i < 1; i++)
{
    Console.WriteLine($"FormatParameterName({i}): '{filterBuilder.QueryFormatProvider.FormatParameterName(i)}'");
}
Console.WriteLine();

// Test different parameter approaches with a mock connection string
Console.WriteLine("Testing different parameter approaches:");

// Approach 1: Dictionary with $1 keys
var dict1 = new Dictionary<string, object?> { { "$1", "John Doe" } };
Console.WriteLine($"Approach 1 - Dictionary with $1 key: {string.Join(", ", dict1.Select(p => $"{p.Key}={p.Value}"))}");

// Approach 2: Dictionary with 1 keys
var dict2 = new Dictionary<string, object?> { { "1", "John Doe" } };
Console.WriteLine($"Approach 2 - Dictionary with 1 key: {string.Join(", ", dict2.Select(p => $"{p.Key}={p.Value}"))}");

// Approach 3: Anonymous object
var obj3 = new { param1 = "John Doe" };
Console.WriteLine($"Approach 3 - Anonymous object: param1=John Doe");

// Approach 4: DynamicParameters
var dynParams = new DynamicParameters();
dynParams.Add("param1", "John Doe");
Console.WriteLine($"Approach 4 - DynamicParameters: param1=John Doe");
