using System.Data;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for ADO.NET.
/// Contains the WHERE clause and database parameters ready for use with ADO.NET commands.
/// </summary>
public class AdoNetQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the database parameters ready for ADO.NET commands.
    /// These are provider-specific parameter objects (e.g., SqlParameter, MySqlParameter).
    /// </summary>
    public IDbDataParameter[] Parameters { get; }

    /// <summary>
    /// Gets the raw parameter values for scenarios where you need to create your own parameters.
    /// This is useful when the provider doesn't implement ToAdoNetParameters or you need custom parameter creation.
    /// </summary>
    public object[] ParameterValues { get; }

    /// <summary>
    /// Creates a new ADO.NET query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameters">The ADO.NET database parameters</param>
    /// <param name="parameterValues">The raw parameter values</param>
    public AdoNetQueryResult(string whereClause, IDbDataParameter[] parameters, object[] parameterValues)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        Parameters = parameters ?? throw new System.ArgumentNullException(nameof(parameters));
        ParameterValues = parameterValues ?? throw new System.ArgumentNullException(nameof(parameterValues));
    }

    /// <summary>
    /// Creates a new ADO.NET query result with empty parameters array.
    /// Use this when the provider doesn't implement ToAdoNetParameters and you'll create parameters manually.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The raw parameter values</param>
    public AdoNetQueryResult(string whereClause, object[] parameterValues)
        : this(whereClause, new IDbDataParameter[0], parameterValues)
    {
    }
}
