namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for Entity Framework 6.
/// Contains the WHERE clause, formatted query with placeholders, and parameter values for EF6's SqlQuery method.
/// </summary>
public class EfQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the query formatted for EF6 SqlQuery with {0}, {1} placeholders.
    /// Example: "[Name] = {0} AND [Age] > {1}"
    /// </summary>
    public string FormattedQuery { get; }

    /// <summary>
    /// Gets the parameter values in the correct order for the formatted query.
    /// These values correspond to the {0}, {1}, etc. placeholders in FormattedQuery.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Creates a new EF6 query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="formattedQuery">The EF6 formatted query with placeholders</param>
    /// <param name="parameters">The parameter values</param>
    public EfQueryResult(string whereClause, string formattedQuery, object[] parameters)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        FormattedQuery = formattedQuery ?? throw new System.ArgumentNullException(nameof(formattedQuery));
        Parameters = parameters ?? throw new System.ArgumentNullException(nameof(parameters));
    }
}
