{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\dapperextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\dapperextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\adonetintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\adonetintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\complexrulestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\complexrulestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.postgresql\\postgresqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|solutionrelative:src\\q.filterbuilder.postgresql\\postgresqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{60C60E09-2D48-404F-BD9B-FB2DA98597D9}|src\\Q.FilterBuilder.MySql\\Q.FilterBuilder.MySql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.mysql\\mysqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{60C60E09-2D48-404F-BD9B-FB2DA98597D9}|src\\Q.FilterBuilder.MySql\\Q.FilterBuilder.MySql.csproj|solutionrelative:src\\q.filterbuilder.mysql\\mysqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\dapperintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\dapperintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\efcoreintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\efcoreintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\datatypetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\datatypetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 497, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 10, "Title": "DapperIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "ViewState": "AgIAAF4AAAAAAAAAAAAmwG8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:04:22.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DataTypeTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "ViewState": "AgIAAKIAAAAAAAAAAAAuwLYAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:59:01.404Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ComplexRulesTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "ViewState": "AgIAABUAAAAAAAAAAAAcwFUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:51:30.629Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AdoNetIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "ViewState": "AgIAAFoAAAAAAAAAAAArwGQAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:35:48.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.test.json", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T09:16:53.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "EFCoreIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAmwBEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:47:50.958Z", "EditorCaption": ""}]}, {"DockedWidth": 665, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "DapperExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwBcAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:16:58.172Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SqlServerFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAIwBsAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:16:38.101Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "PostgreSqlFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAcwBkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:16:13.882Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AdoNetExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwA4AAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:41:05.783Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrmExecutionService.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ViewState": "AgIAACEAAAAAAAAAAAAcwCcAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:39:19.719Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "IntegrationTestController.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAgwG4AAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:35:56.576Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MySqlFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwBkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:15:39.492Z", "EditorCaption": ""}]}]}]}