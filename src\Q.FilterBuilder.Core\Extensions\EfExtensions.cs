using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Entity Framework 6 integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class EfExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework 6.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfQueryResult with formatted query and parameters.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForEf(filterGroup);
    /// var users = context.Users.SqlQuery($"SELECT * FROM Users WHERE {result.FormattedQuery}", result.Parameters);
    /// </code>
    /// </example>
    public static EfQueryResult BuildForEf(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        // EF6 uses same format as EF Core - reuse the EF Core logic
        var efCoreResult = filterBuilder.BuildForEfCore(group);
        return new EfQueryResult(query, efCoreResult.FormattedQuery, parameters);
    }
}
