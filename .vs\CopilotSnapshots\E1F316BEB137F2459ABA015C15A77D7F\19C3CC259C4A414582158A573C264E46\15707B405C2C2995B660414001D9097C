﻿# Q.FilterBuilder.E2E.Tests

True end-to-end (E2E) test project for Q.FilterBuilder that validates the complete workflow from JSON input to actual database query execution using **Testcontainers** and **ASP.NET Core Test Server**.

## Overview

This test project validates the **complete real-world workflow** via web API endpoints:

1. **JSON Input** - Send JSON from UI query builders via HTTP POST
2. **Web API Processing** - ASP.NET Core controller receives and processes JSON
3. **FilterGroup Creation** - JSON deserialized to Q.FilterBuilder FilterGroup objects
4. **Query Generation** - Generate provider-specific SQL using Q.FilterBuilder
5. **Database Execution** - Execute generated queries against **real databases** in Docker containers using **Dapper**
6. **HTTP Response** - Return results via JSON API response

**Key Features**:
- **Single test suite** - Same tests run against all providers
- **Provider selection** - Choose provider via configuration/environment variables
- **Real database containers** - Testcontainers automatically manage database lifecycle
- **Web test server** - Tests actual HTTP API endpoints
- **Complete workflow** - From JSON input to database results

## Project Structure

```
Q.FilterBuilder.E2E.Tests/
├── Configuration/         # Provider configuration and settings
│   ├── DatabaseProviderConfiguration.cs
│   └── TestSettingsProvider.cs
├── Controllers/           # ASP.NET Core test controllers
│   └── E2ETestController.cs
├── Database/             # Entity models and data seeding
│   ├── TestDbContext.cs
│   └── TestDataSeeder.cs
├── Infrastructure/       # Test base classes and startup
│   ├── E2ETestBase.cs
│   └── TestStartup.cs
├── Tests/                # Unified E2E test classes
│   └── CompleteWorkflowE2ETests.cs
├── JsonSamples/          # Sample JSON payloads
│   ├── basic-filter.json
│   ├── complex-nested-filter.json
│   ├── react-querybuilder-format.json
│   └── data-types-comprehensive.json
├── appsettings.test.json # Test configuration
└── run-tests.ps1         # PowerShell test runner
```

## Testcontainers Integration

The E2E tests use **Testcontainers for .NET** to automatically manage database containers:

### Supported Databases
- **SQL Server** - `mcr.microsoft.com/mssql/server:2022-latest`
- **MySQL** - `mysql:8.0`
- **PostgreSQL** - `postgres:15`

### Automatic Container Management
- Containers are **automatically started** before each test class
- Containers are **automatically cleaned up** after each test class
- Each test class gets **fresh database instances**
- No manual Docker setup required

### Test Data
Each container is automatically populated with:
- **Users table** - 4 test users with various attributes
- **Categories table** - 3 product categories
- **Products table** - 4 test products linked to categories

### Prerequisites
- Docker Desktop must be running
- No additional setup required - Testcontainers handles everything

## Test Classes

### CompleteWorkflowE2ETests
**Unified E2E tests that run against the selected provider:**

- `CompleteWorkflow_BasicStringFilter_ShouldReturnCorrectResults` - Basic string equality filter
- `CompleteWorkflow_NumericRangeFilter_ShouldReturnCorrectResults` - Age range filtering
- `CompleteWorkflow_BooleanFilter_ShouldReturnCorrectResults` - Boolean field filtering
- `CompleteWorkflow_StringContainsFilter_ShouldReturnCorrectResults` - String contains operations
- `CompleteWorkflow_InOperatorFilter_ShouldReturnCorrectResults` - IN clause operations
- `CompleteWorkflow_NullCheckFilter_ShouldReturnCorrectResults` - NULL/NOT NULL checks
- `CompleteWorkflow_ComplexNestedFilter_ShouldReturnCorrectResults` - Complex nested groups
- `CompleteWorkflow_ReactQueryBuilderFormat_ShouldReturnCorrectResults` - React QueryBuilder JSON format
- `CompleteWorkflow_JoinQueryWithProducts_ShouldReturnCorrectResults` - JOIN operations
- `CompleteWorkflow_EmptyFilter_ShouldReturnAllResults` - Empty filter handling
- `CompleteWorkflow_SqlInjectionAttempt_ShouldBePreventedByParameterization` - Security testing
- `HealthCheck_ShouldReturnDatabaseStats` - Database connectivity verification

**Provider Selection**: The same test suite runs against different providers based on configuration:
- **SQL Server** - Default provider
- **MySQL** - Set via environment variable or configuration
- **PostgreSQL** - Set via environment variable or configuration

## JSON Sample Files

### Basic Filter
```json
{
  "condition": "AND",
  "rules": [
    {
      "field": "Name",
      "operator": "equal",
      "value": "John Doe",
      "type": "string"
    }
  ]
}
```

### React QueryBuilder Format
```json
{
  "combinator": "and",
  "rules": [
    {
      "field": "firstName",
      "operator": "=",
      "value": "John"
    }
  ]
}
```

### Complex Nested Filter
Supports unlimited nesting with multiple operators and data types.

## Running Tests

### Prerequisites
1. **Docker Desktop must be running** - Testcontainers requires Docker to create database containers
2. **Internet connection** - Required to pull Docker images on first run

### Using PowerShell Script (Recommended)
```bash
# Run tests against all providers
.\run-tests.ps1

# Run tests against specific provider
.\run-tests.ps1 -Provider SqlServer
.\run-tests.ps1 -Provider MySql
.\run-tests.ps1 -Provider PostgreSql

# Run with verbose output
.\run-tests.ps1 -Provider SqlServer -Verbose

# Skip Docker check if detection fails but Docker is running
.\run-tests.ps1 -SkipDockerCheck
```

### Using dotnet test directly
```bash
# Default provider (SQL Server)
dotnet test test/Q.FilterBuilder.E2E.Tests/

# Specify provider via environment variable
$env:Q_FILTERBUILDER_PROVIDER="MySql"
dotnet test test/Q.FilterBuilder.E2E.Tests/

# PostgreSQL with environment variable
$env:Q_FILTERBUILDER_PROVIDER="PostgreSql"
dotnet test test/Q.FilterBuilder.E2E.Tests/
```

### Individual Test Methods
```bash
# Test specific workflow
dotnet test --filter "CompleteWorkflow_BasicStringFilter_ShouldReturnCorrectResults"

# Test health checks
dotnet test --filter "HealthCheck_ShouldReturnDatabaseStats"
```

## Example Test Flow

Here's how a typical E2E test works:

```csharp
[Fact]
public async Task CompleteWorkflow_BasicStringFilter_ShouldReturnCorrectResults()
{
    // 1. Arrange - Create JSON from UI query builder
    var filterJson = JsonDocument.Parse("""
    {
        "condition": "AND",
        "rules": [
            {
                "field": "Name",
                "operator": "equal",
                "value": "John Doe",
                "type": "string"
            }
        ]
    }
    """);

    // 2. Act - Send HTTP POST to E2E controller endpoint
    var response = await _client.PostAsJsonAsync("/api/E2ETest/execute-filter", filterJson);

    // 3. Assert - Verify complete workflow succeeded
    response.EnsureSuccessStatusCode();
    var result = await response.Content.ReadFromJsonAsync<dynamic>();
    Assert.NotNull(result);
}
```

**What happens inside the controller:**
1. JSON is deserialized to FilterGroup using QueryBuilderConverter
2. FilterBuilder generates provider-specific SQL query
3. Dapper executes the query against the real database container
4. Results are returned as JSON response

## Test Coverage

The E2E tests cover:

### Data Types
- ✅ String operations (equal, contains, begins_with, ends_with)
- ✅ Numeric operations (equal, greater, less, between)
- ✅ Boolean operations (equal)
- ✅ DateTime operations (equal, greater, less, between)
- ✅ Array operations (in, not_in)
- ✅ Null checks (is_null, is_not_null)

### Operators
- ✅ Basic operators (equal, not_equal, greater, less)
- ✅ String operators (contains, begins_with, ends_with, like)
- ✅ Range operators (between, not_between)
- ✅ Collection operators (in, not_in)
- ✅ Null operators (is_null, is_not_null)

### JSON Formats
- ✅ jQuery QueryBuilder format
- ✅ React QueryBuilder format
- ✅ Custom property names
- ✅ Metadata support
- ✅ Nested groups

### Database Scenarios
- ✅ Simple queries
- ✅ Complex nested queries
- ✅ Join queries
- ✅ Performance testing
- ✅ Error handling

## Performance Testing

Performance tests validate:
- Query generation time < 5000ms
- JSON deserialization time < 5000ms
- Database query execution time
- Memory usage patterns

## Integration with UI Libraries

The E2E tests validate compatibility with:
- [jQuery QueryBuilder](https://querybuilder.js.org/)
- [React QueryBuilder](https://react-querybuilder.js.org/)

## Troubleshooting

### Database Connection Issues
1. Verify connection strings in `appsettings.test.json`
2. Ensure database servers are running
3. Check firewall settings for Docker containers

### Test Failures
1. Check provider availability in configuration
2. Verify test data seeding completed successfully
3. Review logs for detailed error messages

### Docker Issues
1. Ensure Docker is running
2. Check port conflicts (1433, 3306, 5432)
3. Verify Docker Compose file syntax

## Contributing

When adding new E2E tests:

1. Follow existing naming conventions
2. Test all enabled providers
3. Include both positive and negative test cases
4. Add corresponding JSON sample files
5. Update documentation

## Dependencies

- **Microsoft.AspNetCore.Mvc.Testing** - ASP.NET Core testing
- **Microsoft.EntityFrameworkCore.InMemory** - In-memory database testing
- **Database providers** - SQL Server, MySQL, PostgreSQL connectors
- **xUnit** - Test framework
- **System.Text.Json** - JSON serialization
