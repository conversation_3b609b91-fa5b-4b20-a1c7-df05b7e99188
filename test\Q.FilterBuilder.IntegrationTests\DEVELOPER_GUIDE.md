# Q.FilterBuilder Integration Tests - Developer Guide

## 🚀 Quick Start

### Running All Tests
```bash
# Run all tests with default provider (SQL Server)
dotnet test test/Q.FilterBuilder.IntegrationTests/

# Run tests for specific provider
$env:DatabaseProvider="PostgreSql"
dotnet test test/Q.FilterBuilder.IntegrationTests/
```

## 🏗️ Architecture Overview

### Provider Strategy Pattern
```csharp
// Each provider implements IProviderStrategy
public interface IProviderStrategy
{
    DatabaseProvider Provider { get; }
    void ConfigureFilterBuilder(IServiceCollection services);
    void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString);
    string GetConnectionString(IConfiguration configuration);
    Task<bool> ValidateProviderAsync(string connectionString);
    ProviderTestConfiguration GetTestConfiguration();
}
```

### Supported Providers
- **SQL Server**: Full container-based testing with Testcontainers
- **MySQL**: Full container-based testing with Testcontainers  
- **PostgreSQL**: Full container-based testing with Testcontainers

## 🧪 Writing Tests

### Basic Test Structure
```csharp
[Collection("DatabaseCollection")]
public class MyProviderTests : IntegrationTestBase
{
    public MyProviderTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture) 
        : base(factory, containerFixture)
    {
    }

    [Fact]
    public async Task MyTest_ShouldWork()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("John", content);
    }
}
```

## 🔧 Adding New Providers

### 1. Create Provider Strategy
```csharp
public class MyNewProviderStrategy : IProviderStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.MyNewProvider;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddMyNewProviderFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseMyNewProvider(connectionString);
    }

    // Implement other interface methods...
}
```

### 2. Update Provider Enum
```csharp
public enum DatabaseProvider
{
    SqlServer,
    MySql,
    PostgreSql,
    MyNewProvider  // Add here
}
```

### 3. Register in Factory
```csharp
public class ProviderStrategyFactory
{
    public ProviderStrategyFactory()
    {
        _strategies = new Dictionary<DatabaseProvider, IProviderStrategy>
        {
            [DatabaseProvider.SqlServer] = new SqlServerProviderStrategy(),
            [DatabaseProvider.MySql] = new MySqlProviderStrategy(),
            [DatabaseProvider.PostgreSql] = new PostgreSqlProviderStrategy(),
            [DatabaseProvider.MyNewProvider] = new MyNewProviderStrategy()  // Add here
        };
    }
}
```

### 4. Update Container Fixture (if needed)
```csharp
public async Task InitializeAsync()
{
    switch (Provider)
    {
        // ... existing cases ...
        case DatabaseProvider.MyNewProvider:
            // Setup container or connection logic
            break;
    }
}
```

## 📊 Test Categories

### 1. Basic Operations
- String filters (equal, contains, begins_with, etc.)
- Numeric filters (equal, greater, less, between, etc.)
- Boolean filters (equal, not_equal)
- Date filters (equal, greater, less, between, etc.)
- Null checks (is_null, is_not_null)

### 2. Complex Queries
- Nested groups with AND/OR conditions
- Multiple levels of nesting
- Mixed condition types

### 3. Array Operations
- IN clauses with multiple values
- NOT IN operations
- Array parameter handling

### 4. Error Scenarios
- Invalid field names
- Malformed JSON
- Unsupported operators
- Type conversion errors

### 5. Multi-ORM Support
- Entity Framework Core
- Dapper
- ADO.NET

## 🐳 Container Management

### Automatic Container Lifecycle
- Containers are automatically created and started
- Shared across all tests in the same provider
- Automatically cleaned up after test completion

### Manual Container Control
```csharp
// Containers are managed by DatabaseContainerFixture
// No manual intervention needed in most cases
```

## 🔍 Debugging

### Environment Variables
```bash
# Set specific provider
export DatabaseProvider=MySql

# Enable Docker usage
export UseDocker=true

# Set custom connection string
export ConnectionStrings__TestDatabase="your-connection-string"
```

### Logging
- ASP.NET Core logging is enabled by default
- Entity Framework command logging shows SQL queries
- Testcontainers provides container lifecycle logs

### Common Issues
1. **Docker not running**: Ensure Docker Desktop is started
2. **Port conflicts**: Testcontainers handles port allocation automatically
3. **Provider not found**: Check provider strategy registration
4. **Connection failures**: Verify container startup and health checks

## 🎯 Best Practices

### Test Design
- Use meaningful test names that describe the scenario
- Include provider-specific skip logic when needed
- Test both success and failure scenarios
- Verify both query generation and execution results

### JSON Structure
- Use the correct nested rules format (no separate `groups` key)
- Handle missing `value` properties (defaults to null)
- Include appropriate `type` information for better conversion

### Provider Strategy
- Keep provider-specific logic isolated in strategies
- Use dependency injection for testability
- Implement proper validation and error handling
- Follow consistent naming conventions

This guide provides everything needed to work with the improved Q.FilterBuilder integration test architecture!
