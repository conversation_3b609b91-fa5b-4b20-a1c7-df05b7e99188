# Q.FilterBuilder Extension Guidelines

This document provides guidelines for organizing and extending Q.FilterBuilder with new adapters and result types following SOLID principles.

## File Organization Structure

### Core Models (Result Types)
Each adapter should have its own result type file in `src/Q.FilterBuilder.Core/Models/`:

```
src/Q.FilterBuilder.Core/Models/
├── DapperQueryResult.cs          # Dapper-specific result
├── EfCoreQueryResult.cs          # Entity Framework Core result
├── EfQueryResult.cs              # Entity Framework 6 result
├── AdoNetQueryResult.cs          # ADO.NET result
└── [NewAdapter]QueryResult.cs   # Future adapters
```

### Core Extensions (Adapter Methods)
Each adapter should have its own extension file in `src/Q.FilterBuilder.Core/Extensions/`:

```
src/Q.FilterBuilder.Core/Extensions/
├── DapperExtensions.cs           # Dapper extension methods
├── EfCoreExtensions.cs           # EF Core extension methods
├── EfExtensions.cs               # EF6 extension methods
├── AdoNetExtensions.cs           # ADO.NET extension methods
└── [NewAdapter]Extensions.cs    # Future adapters
```

### Provider-Specific Extensions
Database providers can add optimized implementations in their own projects:

```
src/Q.FilterBuilder.[Provider]/Extensions/
├── [Provider]FilterBuilderExtensions.cs    # Provider-specific optimizations
└── [Provider][Adapter]Extensions.cs        # Provider + Adapter specific
```

## Adding New Adapters

### Step 1: Create Result Type
Create a new file `src/Q.FilterBuilder.Core/Models/[AdapterName]QueryResult.cs`:

```csharp
namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for [AdapterName].
/// [Description of what this adapter provides]
/// </summary>
public class [AdapterName]QueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the [adapter-specific format description].
    /// </summary>
    public [AdapterSpecificType] [AdapterSpecificProperty] { get; }

    /// <summary>
    /// Creates a new [AdapterName] query result.
    /// </summary>
    public [AdapterName]QueryResult(string whereClause, [AdapterSpecificType] adapterSpecificProperty)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        [AdapterSpecificProperty] = adapterSpecificProperty ?? throw new System.ArgumentNullException(nameof(adapterSpecificProperty));
    }
}
```

### Step 2: Create Extension Methods
Create a new file `src/Q.FilterBuilder.Core/Extensions/[AdapterName]Extensions.cs`:

```csharp
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support [AdapterName] integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class [AdapterName]Extensions
{
    /// <summary>
    /// Builds a query result specifically formatted for [AdapterName].
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A [AdapterName]QueryResult with [description].</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildFor[AdapterName](filterGroup);
    /// // Usage example here
    /// </code>
    /// </example>
    public static [AdapterName]QueryResult BuildFor[AdapterName](this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        
        // Convert to adapter-specific format
        var adapterSpecificResult = ConvertToAdapterFormat(query, parameters, filterBuilder.QueryFormatProvider);
        
        return new [AdapterName]QueryResult(query, adapterSpecificResult);
    }

    /// <summary>
    /// Helper method to convert query and parameters to adapter-specific format.
    /// </summary>
    private static [AdapterSpecificType] ConvertToAdapterFormat(
        string query, 
        object[] parameters, 
        Providers.IQueryFormatProvider provider)
    {
        // Implementation specific to the adapter
        // This is where you convert SQL syntax to adapter syntax
        throw new System.NotImplementedException("Implement adapter-specific conversion logic");
    }
}
```

### Step 3: Add Provider-Specific Optimizations (Optional)
If a database provider wants to optimize for the new adapter, create:
`src/Q.FilterBuilder.[Provider]/Extensions/[Provider][AdapterName]Extensions.cs`:

```csharp
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.[Provider].Extensions;

/// <summary>
/// [Provider] specific extension methods for [AdapterName] integration.
/// These extensions provide optimized implementations for [Provider] database operations.
/// </summary>
public static class [Provider][AdapterName]Extensions
{
    /// <summary>
    /// Builds a query result specifically optimized for [Provider] [AdapterName] operations.
    /// </summary>
    public static [AdapterName]QueryResult BuildFor[Provider][AdapterName](this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        
        // Provider-specific optimizations here
        var optimizedResult = CreateProviderOptimizedResult(query, parameters);
        
        return new [AdapterName]QueryResult(query, optimizedResult);
    }
}
```

## Special Cases

### Dynamic LINQ
Dynamic LINQ does not require a separate extension method. Instead, use the existing `Q.FilterBuilder.Linq.LinqFormatProvider`:

```csharp
// Use LinqFormatProvider for Dynamic LINQ compatibility
var linqFilterBuilder = new FilterBuilder(new LinqFormatProvider());
var (query, parameters) = linqFilterBuilder.Build(filterGroup);

// The query is already in LINQ-compatible format
var results = queryableData.Where(query, parameters);
```

The LinqFormatProvider already provides:
- LINQ operators (`&&`, `||` instead of `AND`, `OR`)
- No field name formatting (property names as-is)
- LINQ-compatible parameter naming
- LINQ-specific rule transformers

## Implementation Guidelines

### Result Type Guidelines
1. **Immutable Properties**: All properties should be read-only with private setters or init-only
2. **Null Safety**: Always validate constructor parameters for null
3. **Clear Documentation**: Document what each property contains and how to use it
4. **Consistent Naming**: Follow the pattern `[AdapterName]QueryResult`

### Extension Method Guidelines
1. **Single Responsibility**: Each extension file should handle one adapter only
2. **SOLID Compliance**: Never modify core classes, only extend through extension methods
3. **Clear Examples**: Always provide usage examples in XML documentation
4. **Error Handling**: Validate inputs and provide meaningful error messages
5. **Consistent Naming**: Follow the pattern `BuildFor[AdapterName]`

### Provider Extension Guidelines
1. **Optional Optimizations**: Provider extensions should be optional enhancements
2. **Fallback Support**: Core extensions should work without provider extensions
3. **Clear Benefits**: Document why the provider-specific version is better
4. **Consistent Patterns**: Follow the same patterns as existing provider extensions

## Testing Guidelines

### Unit Tests
Create tests in `test/Q.FilterBuilder.Core.Tests/` following this pattern:

```csharp
[Fact]
public void BuildFor[AdapterName]_ShouldCreateCorrectResult()
{
    // Arrange
    var filterGroup = new FilterGroup("AND");
    filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

    // Act
    var result = _filterBuilder.BuildFor[AdapterName](filterGroup);

    // Assert
    Assert.NotNull(result);
    Assert.Contains("Name", result.WhereClause);
    // Add adapter-specific assertions
}
```

### Integration Tests
Add integration tests that verify the adapter works with actual libraries/frameworks.

## Best Practices

1. **Keep It Simple**: Don't over-engineer - start with basic functionality
2. **Follow Patterns**: Look at existing adapters for consistency
3. **Document Everything**: Provide clear documentation and examples
4. **Test Thoroughly**: Write comprehensive tests for all scenarios
5. **Performance Conscious**: Consider performance implications of conversions
6. **Backward Compatible**: Don't break existing functionality

## Future Considerations

When adding new adapters, consider:
- **Parameter Handling**: How does the adapter handle parameters?
- **Type Conversion**: Does the adapter need special type handling?
- **Performance**: Are there performance optimizations possible?
- **Error Handling**: How should errors be handled?
- **Extensibility**: Can the adapter be extended further?

This structure ensures that Q.FilterBuilder remains maintainable, extensible, and follows SOLID principles while making it easy to add new adapters in the future.
