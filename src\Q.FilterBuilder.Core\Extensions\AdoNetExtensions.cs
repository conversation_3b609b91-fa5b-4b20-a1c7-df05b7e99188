using System;
using System.Data.Common;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support ADO.NET integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class AdoNetExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for ADO.NET.
    /// </summary>
    /// <param name="filterBuilder"></param>
    /// <param name="group"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    /// <code>
    /// var command = connection.CreateCommand();
    /// var result = filterBuilder.BuildForAdoNet(filterGroup, command);
    /// command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";
    /// </code>
    public static AdoNetQueryResult BuildForAdoNet(this IFilterBuilder filterBuilder, FilterGroup group, DbCommand command)
    {
        var (query, parameters) = filterBuilder.Build(group);
        command.AddParameters(parameters, filterBuilder.QueryFormatProvider);
        return new AdoNetQueryResult(query, parameters);
    }

    /// <summary>
    /// Helper method to create database parameters using the provided connection.
    /// </summary>
    /// <param name="command"></param>
    /// <param name="parameters"></param>
    /// <param name="provider"></param>
    public static void AddParameters(this DbCommand command, object[] parameters, IQueryFormatProvider provider)
    {
        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = provider.FormatParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }
}
