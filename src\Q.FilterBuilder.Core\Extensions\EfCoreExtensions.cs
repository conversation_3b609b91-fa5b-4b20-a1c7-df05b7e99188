using System.Text.RegularExpressions;
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Entity Framework Core integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class EfCoreExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework Core.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfCoreQueryResult with formatted query and parameters.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForEfCore(filterGroup);
    /// var users = context.Users.FromSqlRaw($"SELECT * FROM Users WHERE {result.FormattedQuery}", result.Parameters);
    /// </code>
    /// </example>
    public static EfCoreQueryResult BuildForEfCore(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var formattedQuery = ConvertToEfCoreFormat(query, filterBuilder.QueryFormatProvider);
        return new EfCoreQueryResult(query, formattedQuery, parameters);
    }

    /// <summary>
    /// Converts a raw WHERE clause to EF Core format with {0}, {1} placeholders.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="provider">The query format provider</param>
    /// <returns>The EF Core formatted query</returns>
    private static string ConvertToEfCoreFormat(string whereClause, Providers.IQueryFormatProvider provider)
    {
        var efQuery = whereClause;

        // Handle different parameter formats based on provider
        switch (provider.ParameterPrefix)
        {
            case "$": // PostgreSQL: $1, $2, etc. -> {0}, {1}, etc.
                efQuery = Regex.Replace(efQuery, @"\$(\d+)", match =>
                {
                    var paramIndex = int.Parse(match.Groups[1].Value) - 1; // Convert 1-based to 0-based
                    return $"{{{paramIndex}}}";
                });
                break;

            case "?": // MySQL: ? -> {0}, {1}, etc. (handled by counting occurrences)
                var paramIndex = 0;
                efQuery = Regex.Replace(efQuery, @"\?", _ => $"{{{paramIndex++}}}");
                break;

            default: // SQL Server: @p0, @p1, etc. -> {0}, {1}, etc.
                var parameterPattern = Regex.Escape(provider.ParameterPrefix) + @"p(\d+)";
                efQuery = Regex.Replace(efQuery, parameterPattern, "{$1}");
                break;
        }

        return efQuery;
    }
}
